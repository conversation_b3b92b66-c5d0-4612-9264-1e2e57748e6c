<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Message Preview</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 2rem;
            background: #f5f5f5;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1>Analysis Message Format Preview</h1>
        
        <!-- Example Analysis Message -->
        <div class="message-item analysis-message">
            <div class="analysis-header">
                <div class="message-timestamp">2025-06-16 10:05:30</div>
                <div class="threat-level-badge threat-critical">
                    <span class="threat-level-number">8</span>
                    <span class="threat-level-label">CRITICAL</span>
                </div>
            </div>
            
            <div class="analysis-content">
                <div class="threat-levels-section">
                    <h4>Threat Assessment</h4>
                    <div class="threat-levels-grid">
                        <div class="threat-item">
                            <span class="threat-source">Field Reports</span>
                            <span class="threat-value level-7">7</span>
                        </div>
                        <div class="threat-item">
                            <span class="threat-source">Image Analysis</span>
                            <span class="threat-value level-8">8</span>
                        </div>
                        <div class="threat-item">
                            <span class="threat-source">Combined</span>
                            <span class="threat-value level-8">8</span>
                        </div>
                    </div>
                </div>

                <div class="situation-section">
                    <h4>Security Situation</h4>
                    <p class="situation-summary">High threat level detected in the northern sector. Multiple hostile contacts identified through satellite imagery and field reconnaissance. Immediate action required to secure the perimeter and evacuate non-essential personnel.</p>
                </div>

                <div class="actions-section">
                    <h4>Recommended Actions</h4>
                    <ul class="actions-list">
                        <li class="action-item">Deploy additional security forces to northern perimeter</li>
                        <li class="action-item">Initiate evacuation protocol for civilian personnel</li>
                        <li class="action-item">Establish communication blackout procedures</li>
                        <li class="action-item">Request immediate air support for reconnaissance</li>
                    </ul>
                </div>

                <div class="location-section">
                    <h4>Location Data</h4>
                    <div class="location-content">
                        <div class="location-grid">
                            <div class="location-item">
                                <span class="location-label">Region ID</span>
                                <span class="location-value">42</span>
                            </div>
                            <div class="location-item">
                                <span class="location-label">Base ID</span>
                                <span class="location-value">101</span>
                            </div>
                            <div class="location-item">
                                <span class="location-label">Coordinates</span>
                                <span class="location-value">34.0522, -118.2437</span>
                            </div>
                        </div>
                        <div class="map-container">
                            <div class="map-header">
                                <span class="map-title">📍 Location Map</span>
                                <span class="map-coords">34.0522, -118.2437</span>
                            </div>
                            <div id="preview-map" class="location-map"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Example of different threat levels -->
        <div style="margin-top: 2rem;">
            <h2 style="color: #333; margin-bottom: 1rem;">Threat Level Examples</h2>
            
            <div class="message-item analysis-message" style="margin-bottom: 1rem;">
                <div class="analysis-header">
                    <div class="message-timestamp">2025-06-16 09:30:15</div>
                    <div class="threat-level-badge threat-low">
                        <span class="threat-level-number">2</span>
                        <span class="threat-level-label">LOW</span>
                    </div>
                </div>
                <div class="analysis-content">
                    <div class="situation-section">
                        <h4>Security Situation</h4>
                        <p class="situation-summary">Routine patrol reports normal activity. No immediate threats detected.</p>
                    </div>
                </div>
            </div>

            <div class="message-item analysis-message">
                <div class="analysis-header">
                    <div class="message-timestamp">2025-06-16 09:45:22</div>
                    <div class="threat-level-badge threat-medium">
                        <span class="threat-level-number">5</span>
                        <span class="threat-level-label">MEDIUM</span>
                    </div>
                </div>
                <div class="analysis-content">
                    <div class="situation-section">
                        <h4>Security Situation</h4>
                        <p class="situation-summary">Increased activity detected in eastern quadrant. Monitoring situation closely.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <script>
        // Initialize the preview map
        document.addEventListener('DOMContentLoaded', function() {
            // Create the map centered on Los Angeles (the coordinates in the example)
            const map = L.map('preview-map').setView([34.0522, -118.2437], 10);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            // Create custom threat marker
            const threatMarker = L.divIcon({
                className: 'threat-marker',
                html: `
                    <div class="threat-marker-inner threat-critical" style="background-color: #dc3545;">
                        <span class="threat-marker-level">8</span>
                        <div class="threat-marker-pulse"></div>
                    </div>
                `,
                iconSize: [40, 40],
                iconAnchor: [20, 20],
                popupAnchor: [0, -20]
            });

            // Add marker
            const marker = L.marker([34.0522, -118.2437], {
                icon: threatMarker
            }).addTo(map);

            // Add popup
            const popupContent = `
                <div class="map-popup">
                    <div class="popup-header">
                        <strong>Region 42</strong>
                        <span class="popup-threat-badge threat-critical">
                            8 - CRITICAL
                        </span>
                    </div>
                    <div class="popup-content">
                        <p><strong>Base ID:</strong> 101</p>
                        <p><strong>Coordinates:</strong> 34.0522, -118.2437</p>
                        <p><strong>Situation:</strong> High threat level detected in the northern sector. Multiple hostile contacts identified...</p>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent, {
                maxWidth: 300,
                className: 'threat-popup'
            });

            // Force map to resize
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        });
    </script>
</body>
</html>
