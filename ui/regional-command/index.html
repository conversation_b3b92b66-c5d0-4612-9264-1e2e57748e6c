<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regional Command Analysis Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Regional Command Analysis Dashboard</h1>
            <div class="connection-status">
                <span id="status-indicator" class="status-indicator disconnected"></span>
                <span id="status-text">Disconnected</span>
            </div>
        </header>

        <div class="controls">
            <div class="control-group">
                <label for="topic-input">Topic:</label>
                <input type="text" id="topic-input" placeholder="Enter topic to subscribe to" />
                <button id="subscribe-btn" class="btn btn-primary">Subscribe</button>
                <button id="unsubscribe-btn" class="btn btn-secondary" disabled>Unsubscribe</button>
            </div>
            
            <div class="control-group">
                <button id="connect-btn" class="btn btn-success">Connect</button>
                <button id="disconnect-btn" class="btn btn-danger" disabled>Disconnect</button>
                <button id="clear-btn" class="btn btn-secondary">Clear Messages</button>
            </div>
        </div>

        <div class="message-controls">
            <div class="control-group">
                <label for="filter-input">Filter reports:</label>
                <input type="text" id="filter-input" placeholder="Search analysis reports..." />
                <label for="auto-scroll">
                    <input type="checkbox" id="auto-scroll" checked> Auto-scroll
                </label>
            </div>
        </div>

        <div class="message-container">
            <div class="message-header">
                <h3>Analysis Reports (<span id="message-count">0</span>)</h3>
                <div class="message-stats">
                    <span>Rate: <span id="message-rate">0</span> reports/s</span>
                </div>
            </div>
            <div id="message-list" class="message-list"></div>
        </div>

        <div class="footer">
            <div class="config-info">
                <span>URL: <span id="config-url">-</span></span>
                <span>VPN: <span id="config-vpn">-</span></span>
                <span>User: <span id="config-username">-</span></span>
            </div>
        </div>
    </div>

    <script src="solace-client.js"></script>
    <script src="renderer.js"></script>
</body>
</html>
