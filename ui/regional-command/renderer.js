const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class MessageConsumerApp {
    constructor() {
        this.solaceClient = new SolaceClient();
        this.messages = [];
        this.messageCount = 0;
        this.messageRate = 0;
        this.lastMessageTime = Date.now();
        this.messageRateInterval = null;
        this.currentTopic = null;
        this.config = null;
        this.maps = new Map(); // Store map instances by message ID
        this.mapCounter = 0; // Counter for unique map IDs

        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
        this.setupSolaceClient();
        this.startMessageRateCalculation();
    }

    initializeElements() {
        // Status elements
        this.statusIndicator = document.getElementById('status-indicator');
        this.statusText = document.getElementById('status-text');
        
        // Control elements
        this.topicInput = document.getElementById('topic-input');
        this.subscribeBtn = document.getElementById('subscribe-btn');
        this.unsubscribeBtn = document.getElementById('unsubscribe-btn');
        this.connectBtn = document.getElementById('connect-btn');
        this.disconnectBtn = document.getElementById('disconnect-btn');
        this.clearBtn = document.getElementById('clear-btn');
        
        // Message elements
        this.filterInput = document.getElementById('filter-input');
        this.autoScrollCheckbox = document.getElementById('auto-scroll');
        this.messageList = document.getElementById('message-list');
        this.messageCount = document.getElementById('message-count');
        this.messageRateDisplay = document.getElementById('message-rate');
        
        // Config display elements
        this.configUrl = document.getElementById('config-url');
        this.configVpn = document.getElementById('config-vpn');
        this.configUsername = document.getElementById('config-username');
    }

    setupEventListeners() {
        this.connectBtn.addEventListener('click', () => this.connect());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        this.subscribeBtn.addEventListener('click', () => this.subscribe());
        this.unsubscribeBtn.addEventListener('click', () => this.unsubscribe());
        this.clearBtn.addEventListener('click', () => this.clearMessages());
        
        this.filterInput.addEventListener('input', () => this.filterMessages());
        
        // Enter key handlers
        this.topicInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.subscribe();
            }
        });
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.updateConfigDisplay();
            this.topicInput.value = this.config.topic || '';
        } catch (error) {
            console.error('Error loading configuration:', error);
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'Failed to load configuration');
        }
    }

    updateConfigDisplay() {
        if (this.config) {
            this.configUrl.textContent = this.config.url;
            this.configVpn.textContent = this.config.vpn;
            this.configUsername.textContent = this.config.username;
        }
    }

    setupSolaceClient() {
        this.solaceClient.setStatusCallback((status, message) => {
            this.updateConnectionStatus(status, message);
        });

        this.solaceClient.setMessageCallback((messageData) => {
            this.addMessage(messageData);
        });
    }

    updateConnectionStatus(status, message) {
        this.statusText.textContent = message;
        
        // Remove all status classes
        this.statusIndicator.classList.remove('connected', 'disconnected', 'connecting');
        
        // Add appropriate class
        this.statusIndicator.classList.add(status);
        
        // Update button states
        const isConnected = status === 'connected';
        const isConnecting = status === 'connecting';
        
        this.connectBtn.disabled = isConnected || isConnecting;
        this.disconnectBtn.disabled = !isConnected && !isConnecting;
        this.subscribeBtn.disabled = !isConnected;
        
        if (!isConnected) {
            this.unsubscribeBtn.disabled = true;
            this.currentTopic = null;
        }
    }

    async connect() {
        if (!this.config) {
            await ipcRenderer.invoke('show-error', 'Configuration Error', 'No configuration available');
            return;
        }

        try {
            await this.solaceClient.connect(this.config);
        } catch (error) {
            console.error('Connection error:', error);
            await ipcRenderer.invoke('show-error', 'Connection Error', `Failed to connect: ${error.message}`);
        }
    }

    disconnect() {
        this.solaceClient.disconnect();
        this.currentTopic = null;
        this.unsubscribeBtn.disabled = true;
    }

    subscribe() {
        const topic = this.topicInput.value.trim();
        if (!topic) {
            ipcRenderer.invoke('show-error', 'Topic Error', 'Please enter a topic name');
            return;
        }

        try {
            // Unsubscribe from current topic if any
            if (this.currentTopic) {
                this.solaceClient.unsubscribe(this.currentTopic);
            }

            this.solaceClient.subscribe(topic);
            this.currentTopic = topic;
            this.unsubscribeBtn.disabled = false;
            
            // Clear previous messages when subscribing to new topic
            this.clearMessages();
            
        } catch (error) {
            console.error('Subscription error:', error);
            ipcRenderer.invoke('show-error', 'Subscription Error', `Failed to subscribe: ${error.message}`);
        }
    }

    unsubscribe() {
        if (this.currentTopic) {
            try {
                this.solaceClient.unsubscribe(this.currentTopic);
                this.currentTopic = null;
                this.unsubscribeBtn.disabled = true;
            } catch (error) {
                console.error('Unsubscription error:', error);
                ipcRenderer.invoke('show-error', 'Unsubscription Error', `Failed to unsubscribe: ${error.message}`);
            }
        }
    }

    addMessage(messageData) {
        this.messages.push(messageData);
        this.updateMessageCount();
        this.renderMessage(messageData);
        
        // Auto-scroll if enabled
        if (this.autoScrollCheckbox.checked) {
            this.messageList.scrollTop = this.messageList.scrollHeight;
        }
    }

    renderMessage(messageData, isFiltered = false) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';
        if (!isFiltered) {
            messageElement.classList.add('new');
        }

        const timestamp = new Date(messageData.timestamp).toLocaleString();

        // Try to parse the payload as analysis data
        let analysisData = null;
        try {
            analysisData = JSON.parse(messageData.payload);
            // Check if it matches the analysis schema structure
            if (this.isAnalysisData(analysisData)) {
                messageElement.innerHTML = this.renderAnalysisMessage(timestamp, messageData.topic, analysisData);
                messageElement.classList.add('analysis-message');
            } else {
                // Fallback to original format
                messageElement.innerHTML = this.renderGenericMessage(timestamp, messageData.topic, messageData.payload);
            }
        } catch (e) {
            // If JSON parsing fails, use original format
            messageElement.innerHTML = this.renderGenericMessage(timestamp, messageData.topic, messageData.payload);
        }

        this.messageList.appendChild(messageElement);

        // Initialize map if this is an analysis message with location data
        if (messageElement.classList.contains('analysis-message')) {
            try {
                const analysisData = JSON.parse(messageData.payload);
                if (analysisData.latitude && analysisData.longitude) {
                    // Find the map container in the newly added element
                    const mapContainer = messageElement.querySelector('.location-map');
                    if (mapContainer) {
                        this.initializeMap(mapContainer.id, analysisData);
                    }
                }
            } catch (e) {
                // Ignore parsing errors
            }
        }

        // Remove 'new' class after animation
        if (!isFiltered) {
            setTimeout(() => {
                messageElement.classList.remove('new');
            }, 300);
        }
    }

    clearMessages() {
        // Clean up all map instances
        this.maps.forEach((map, mapId) => {
            try {
                map.remove();
            } catch (e) {
                console.warn('Error removing map:', e);
            }
        });
        this.maps.clear();
        this.mapCounter = 0;

        this.messages = [];
        this.messageList.innerHTML = '';
        this.updateMessageCount();
    }

    filterMessages() {
        const filterText = this.filterInput.value.toLowerCase();
        this.messageList.innerHTML = '';

        const filteredMessages = this.messages.filter(message => {
            // Check topic
            if (message.topic.toLowerCase().includes(filterText)) {
                return true;
            }

            // Check payload (original behavior)
            if (message.payload.toLowerCase().includes(filterText)) {
                return true;
            }

            // For analysis messages, also check parsed content
            try {
                const analysisData = JSON.parse(message.payload);
                if (this.isAnalysisData(analysisData)) {
                    // Check security situation summary
                    if (analysisData.security_situation_summary.toLowerCase().includes(filterText)) {
                        return true;
                    }

                    // Check recommended actions
                    if (analysisData.recommended_actions.some(action =>
                        action.toLowerCase().includes(filterText))) {
                        return true;
                    }

                    // Check threat levels
                    const threatLevelLabel = this.getThreatLevelLabel(analysisData.threat_level_combined);
                    if (threatLevelLabel.toLowerCase().includes(filterText)) {
                        return true;
                    }

                    // Check location data
                    if (filterText.includes('region') && analysisData.regionId) {
                        return true;
                    }
                    if (filterText.includes('base') && analysisData.baseId) {
                        return true;
                    }
                }
            } catch (e) {
                // If parsing fails, fall back to original payload search
            }

            return false;
        });

        filteredMessages.forEach(message => {
            this.renderMessage(message, true);
        });
    }

    updateMessageCount() {
        this.messageCount.textContent = this.messages.length;
    }

    startMessageRateCalculation() {
        let lastCount = 0;
        this.messageRateInterval = setInterval(() => {
            const currentCount = this.messages.length;
            const rate = currentCount - lastCount;
            this.messageRateDisplay.textContent = rate;
            lastCount = currentCount;
        }, 1000);
    }

    isAnalysisData(data) {
        // Check if the data has the expected analysis schema properties
        return data &&
               typeof data === 'object' &&
               typeof data.threat_level_field_reports === 'number' &&
               typeof data.threat_level_image_analysis === 'number' &&
               typeof data.threat_level_combined === 'number' &&
               typeof data.security_situation_summary === 'string' &&
               Array.isArray(data.recommended_actions);
    }

    renderGenericMessage(timestamp, topic, payload) {
        return `
            <div class="message-timestamp">${timestamp}</div>
            <div class="message-topic">Topic: ${topic}</div>
            <div class="message-payload">${this.escapeHtml(payload)}</div>
        `;
    }

    renderAnalysisMessage(timestamp, topic, analysisData) {
        const threatLevel = analysisData.threat_level_combined;
        const threatClass = this.getThreatLevelClass(threatLevel);
        const threatLabel = this.getThreatLevelLabel(threatLevel);

        return `
            <div class="analysis-header">
                <div class="message-timestamp">${timestamp}</div>
                <div class="threat-level-badge ${threatClass}">
                    <span class="threat-level-number">${threatLevel}</span>
                    <span class="threat-level-label">${threatLabel}</span>
                </div>
            </div>

            <div class="analysis-content">
                <div class="threat-levels-section">
                    <h4>Threat Assessment</h4>
                    <div class="threat-levels-grid">
                        <div class="threat-item">
                            <span class="threat-source">Field Reports</span>
                            <span class="threat-value level-${analysisData.threat_level_field_reports}">${analysisData.threat_level_field_reports}</span>
                        </div>
                        <div class="threat-item">
                            <span class="threat-source">Image Analysis</span>
                            <span class="threat-value level-${analysisData.threat_level_image_analysis}">${analysisData.threat_level_image_analysis}</span>
                        </div>
                        <div class="threat-item">
                            <span class="threat-source">Combined</span>
                            <span class="threat-value level-${analysisData.threat_level_combined}">${analysisData.threat_level_combined}</span>
                        </div>
                    </div>
                </div>

                <div class="situation-section">
                    <h4>Security Situation</h4>
                    <p class="situation-summary">${this.escapeHtml(analysisData.security_situation_summary)}</p>
                </div>

                <div class="actions-section">
                    <h4>Recommended Actions</h4>
                    <ul class="actions-list">
                        ${analysisData.recommended_actions.map(action =>
                            `<li class="action-item">${this.escapeHtml(action)}</li>`
                        ).join('')}
                    </ul>
                </div>

                <div class="location-section">
                    <h4>Location Data</h4>
                    <div class="location-content">
                        <div class="location-grid">
                            <div class="location-item">
                                <span class="location-label">Region ID</span>
                                <span class="location-value">${analysisData.regionId || analysisData.region_id || 'N/A'}</span>
                            </div>
                            <div class="location-item">
                                <span class="location-label">Base ID</span>
                                <span class="location-value">${analysisData.baseId || analysisData.base_id || 'N/A'}</span>
                            </div>
                            <div class="location-item">
                                <span class="location-label">Coordinates</span>
                                <span class="location-value">${analysisData.latitude?.toFixed(4) || 'N/A'}, ${analysisData.longitude?.toFixed(4) || 'N/A'}</span>
                            </div>
                        </div>
                        ${this.renderLocationMap(analysisData)}
                    </div>
                </div>
            </div>
        `;
    }

    getThreatLevelClass(level) {
        if (level >= 8) return 'threat-critical';
        if (level >= 6) return 'threat-high';
        if (level >= 4) return 'threat-medium';
        if (level >= 2) return 'threat-low';
        return 'threat-minimal';
    }

    getThreatLevelLabel(level) {
        if (level >= 8) return 'CRITICAL';
        if (level >= 6) return 'HIGH';
        if (level >= 4) return 'MEDIUM';
        if (level >= 2) return 'LOW';
        return 'MINIMAL';
    }

    renderLocationMap(analysisData) {
        // Check if we have valid coordinates
        if (!analysisData.latitude || !analysisData.longitude) {
            return `
                <div class="map-container">
                    <div class="map-placeholder">
                        <span class="map-placeholder-text">📍 No location data available</span>
                    </div>
                </div>
            `;
        }

        const mapId = `map-${++this.mapCounter}`;

        // Return the map container HTML
        return `
            <div class="map-container">
                <div class="map-header">
                    <span class="map-title">📍 Location Map</span>
                    <span class="map-coords">${analysisData.latitude.toFixed(4)}, ${analysisData.longitude.toFixed(4)}</span>
                </div>
                <div id="${mapId}" class="location-map"></div>
            </div>
        `;
    }

    initializeMap(mapId, analysisData) {
        // Wait for the DOM element to be available
        setTimeout(() => {
            const mapElement = document.getElementById(mapId);
            if (!mapElement) {
                console.warn(`Map element ${mapId} not found`);
                return;
            }

            try {
                // Create the map
                const map = L.map(mapId, {
                    zoomControl: true,
                    scrollWheelZoom: false,
                    doubleClickZoom: true,
                    boxZoom: false,
                    keyboard: false,
                    dragging: true,
                    touchZoom: false
                }).setView([analysisData.latitude, analysisData.longitude], 10);

                // Add tile layer (using OpenStreetMap)
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(map);

                // Create custom marker based on threat level
                const threatLevel = analysisData.threat_level_combined;
                const markerIcon = this.createThreatMarker(threatLevel);

                // Add marker
                const marker = L.marker([analysisData.latitude, analysisData.longitude], {
                    icon: markerIcon
                }).addTo(map);

                // Create popup content
                const popupContent = `
                    <div class="map-popup">
                        <div class="popup-header">
                            <strong>Region ${analysisData.regionId || analysisData.region_id || 'Unknown'}</strong>
                            <span class="popup-threat-badge ${this.getThreatLevelClass(threatLevel)}">
                                ${threatLevel} - ${this.getThreatLevelLabel(threatLevel)}
                            </span>
                        </div>
                        <div class="popup-content">
                            <p><strong>Base ID:</strong> ${analysisData.baseId || analysisData.base_id || 'N/A'}</p>
                            <p><strong>Coordinates:</strong> ${analysisData.latitude.toFixed(4)}, ${analysisData.longitude.toFixed(4)}</p>
                            <p><strong>Situation:</strong> ${analysisData.security_situation_summary.substring(0, 100)}${analysisData.security_situation_summary.length > 100 ? '...' : ''}</p>
                        </div>
                    </div>
                `;

                marker.bindPopup(popupContent, {
                    maxWidth: 300,
                    className: 'threat-popup'
                });

                // Store the map instance
                this.maps.set(mapId, map);

                // Force map to resize after a short delay
                setTimeout(() => {
                    map.invalidateSize();
                }, 100);

            } catch (error) {
                console.error('Error initializing map:', error);
                mapElement.innerHTML = `
                    <div class="map-error">
                        <span>⚠️ Error loading map</span>
                    </div>
                `;
            }
        }, 50);
    }

    createThreatMarker(threatLevel) {
        const color = this.getThreatMarkerColor(threatLevel);
        const size = threatLevel >= 6 ? 'large' : 'medium';

        return L.divIcon({
            className: 'threat-marker',
            html: `
                <div class="threat-marker-inner ${this.getThreatLevelClass(threatLevel)}" style="background-color: ${color};">
                    <span class="threat-marker-level">${threatLevel}</span>
                    ${threatLevel >= 8 ? '<div class="threat-marker-pulse"></div>' : ''}
                </div>
            `,
            iconSize: size === 'large' ? [40, 40] : [30, 30],
            iconAnchor: size === 'large' ? [20, 20] : [15, 15],
            popupAnchor: [0, size === 'large' ? -20 : -15]
        });
    }

    getThreatMarkerColor(level) {
        if (level >= 8) return '#dc3545'; // Critical - Red
        if (level >= 6) return '#fd7e14'; // High - Orange
        if (level >= 4) return '#ffc107'; // Medium - Yellow
        if (level >= 2) return '#28a745'; // Low - Green
        return '#6c757d'; // Minimal - Gray
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MessageConsumerApp();
});
