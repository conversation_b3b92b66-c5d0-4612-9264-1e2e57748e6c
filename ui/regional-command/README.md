# Regional Command Analysis Dashboard

An Electron application for consuming and displaying real-time security analysis reports from Solace PubSub+ topics. The application is specifically designed to handle analysis data formatted according to the analysis schema and display it in an attractive, military-style dashboard format.

## Features

- **Real-time Analysis Display**: View incoming security analysis reports with rich formatting
- **Interactive Location Maps**: Automatically display location data on interactive maps with threat-level markers
- **Threat Level Visualization**: Color-coded threat indicators with severity-based styling
- **Analysis Schema Support**: Automatically detects and formats messages according to analysis.schema.json
- **Configurable Connection**: Use environment variables for Solace connection parameters
- **Topic Subscription Management**: Subscribe/unsubscribe to topics dynamically
- **Advanced Filtering**: Search and filter analysis reports by content, threat level, location, and actions
- **Connection Status**: Visual indicators for connection state
- **Message Rate Monitoring**: Real-time analysis report rate display
- **Auto-scroll**: Optional automatic scrolling for new reports
- **Military-style UI**: Professional dashboard interface designed for command centers

## Prerequisites

- Node.js (v16 or higher)
- Access to a Solace PubSub+ broker

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` with your Solace connection details:
   ```env
   SOLACE_URL=ws://your-solace-broker:8008
   SOLACE_VPN=your-vpn-name
   SOLACE_USERNAME=your-username
   SOLACE_PASSWORD=your-password
   SOLACE_TOPIC=your/default/topic
   ```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Building Distributables
```bash
npm run build
```

## Configuration

The application uses environment variables for configuration. All connection parameters should be set in the `.env` file:

- `SOLACE_URL`: WebSocket or TCP URL of your Solace broker
- `SOLACE_VPN`: Message VPN name
- `SOLACE_USERNAME`: Username for authentication
- `SOLACE_PASSWORD`: Password for authentication
- `SOLACE_TOPIC`: Default topic to display in the subscription field

## Analysis Data Format

The application expects messages with payloads formatted according to the analysis schema. The expected JSON structure includes:

```json
{
    "threat_level_field_reports": 7,
    "threat_level_image_analysis": 8,
    "threat_level_combined": 8,
    "security_situation_summary": "High threat level detected...",
    "recommended_actions": [
        "Deploy additional security forces",
        "Initiate evacuation protocol",
        "Request immediate air support"
    ],
    "regionId": 42,
    "baseId": 101,
    "latitude": 34.0522,
    "longitude": -118.2437
}
```

### Threat Levels
- **1-2**: MINIMAL (Gray) - Routine operations
- **3-4**: LOW (Green) - Minor concerns
- **5-6**: MEDIUM (Yellow) - Elevated attention required
- **7-8**: HIGH (Orange) - Significant threat
- **9-10**: CRITICAL (Red) - Immediate action required

### Map Features
- **Interactive Maps**: Automatically generated for each analysis report with valid coordinates
- **Threat Markers**: Color-coded markers based on combined threat level
- **Popup Information**: Detailed threat information on marker click
- **Pulsing Animation**: Critical threats (8+) display pulsing markers for attention

## Application Interface

### Connection Controls
- **Connect/Disconnect**: Establish or terminate connection to Solace broker
- **Status Indicator**: Visual connection state (green=connected, red=disconnected, orange=connecting)

### Topic Management
- **Topic Input**: Enter the topic pattern to subscribe to
- **Subscribe/Unsubscribe**: Manage topic subscriptions

### Message Display
- **Message List**: Real-time display of incoming messages
- **Message Counter**: Total number of received messages
- **Message Rate**: Messages per second
- **Filter**: Search messages by content or topic
- **Auto-scroll**: Automatically scroll to newest messages
- **Clear**: Remove all displayed messages

### Footer Information
- Displays current connection configuration (URL, VPN, Username)

## Message Format

Each displayed message includes:
- **Timestamp**: When the message was received
- **Topic**: The topic the message was published to
- **Payload**: The message content

## Troubleshooting

### Connection Issues
1. Verify your Solace broker is running and accessible
2. Check the connection parameters in your `.env` file
3. Ensure the WebSocket port (typically 8008) is open
4. Verify your username/password credentials

### Subscription Issues
1. Ensure you're connected before subscribing
2. Check topic permissions for your user
3. Verify topic syntax (use `/` for hierarchy, `*` for wildcards)

### Performance
- For high message rates, consider using message filtering
- Clear messages periodically to maintain performance
- Disable auto-scroll for very high throughput scenarios

## Development

### Project Structure
```
├── main.js           # Main Electron process
├── renderer.js       # Renderer process (UI logic)
├── solace-client.js  # Solace messaging client wrapper
├── index.html        # Main application window
├── styles.css        # Application styling
├── package.json      # Dependencies and scripts
└── .env.example      # Configuration template
```

### Adding Features
- Message persistence
- Export functionality
- Multiple topic subscriptions
- Message publishing capabilities
- Advanced filtering options

## License

MIT License - see LICENSE file for details
