# Regional Command Analysis Dashboard - Usage Examples

## Testing the Application

### 1. Start the Dashboard
```bash
npm run dev
```

### 2. Connect to Solace Broker
- Click "Connect" button
- Subscribe to your analysis topic (e.g., `regional/command/analysis`)

### 3. Send Test Analysis Data

You can use the included test sender to simulate analysis reports:

```bash
node test-sender.js
```

Or manually send JSON data to your Solace topic with the following format:

## Example Analysis Messages

### Low Threat Example
```json
{
    "threat_level_field_reports": 2,
    "threat_level_image_analysis": 3,
    "threat_level_combined": 3,
    "security_situation_summary": "Routine patrol completed. Minor suspicious activity reported in sector 7. Situation under observation.",
    "recommended_actions": [
        "Continue regular patrol schedule",
        "Increase surveillance in sector 7",
        "Report any changes immediately"
    ],
    "regionId": 15,
    "baseId": 203,
    "latitude": 35.6762,
    "longitude": 139.6503
}
```

### High Threat Example
```json
{
    "threat_level_field_reports": 6,
    "threat_level_image_analysis": 7,
    "threat_level_combined": 7,
    "security_situation_summary": "Elevated threat detected. Multiple unidentified vehicles approaching from the southeast. Perimeter sensors triggered.",
    "recommended_actions": [
        "Activate perimeter defense systems",
        "Deploy rapid response team to southeast sector",
        "Establish communication with approaching vehicles",
        "Prepare for potential evacuation if needed"
    ],
    "regionId": 22,
    "baseId": 156,
    "latitude": 40.7128,
    "longitude": -74.0060
}
```

### Critical Threat Example
```json
{
    "threat_level_field_reports": 9,
    "threat_level_image_analysis": 8,
    "threat_level_combined": 9,
    "security_situation_summary": "CRITICAL ALERT: Hostile forces confirmed. Multiple armed contacts detected. Base under immediate threat. All personnel to battle stations.",
    "recommended_actions": [
        "IMMEDIATE: Sound general alarm",
        "Deploy all available defensive units",
        "Initiate lockdown procedures",
        "Request immediate backup from allied forces",
        "Prepare for potential base evacuation"
    ],
    "regionId": 8,
    "baseId": 442,
    "latitude": 32.7767,
    "longitude": -96.7970
}
```

## Dashboard Layout

The dashboard uses an efficient two-column layout:

### Left Column
- **Location Data**: Region ID, Base ID, and coordinates
- **Interactive Map**: Full-featured map showing threat location with color-coded markers
- **Geographic Context**: Immediate visual understanding of threat location

### Right Column
- **Security Situation**: Detailed situation summary and analysis
- **Recommended Actions**: Interactive checklist for tracking action completion
- **Operational Context**: Actions and situation grouped for decision-making

## Dashboard Features

### Visual Elements
- **Threat Level Badges**: Color-coded indicators showing combined threat level
- **Threat Assessment Grid**: Individual scores for field reports, image analysis, and combined threat level
- **Two-Column Layout**: Efficient use of screen space with maps on left, actions on right
- **Interactive Maps**: Full-featured maps with location data in the left column
- **Action Checklists**: Interactive checkboxes for tracking action completion in the right column
- **Security Situation**: Detailed situation summary positioned with actions for context

### Filtering and Search
- Search by threat level: "critical", "high", "medium", "low", "minimal"
- Search by location: "region", "base"
- Search by situation content
- Search by recommended actions

### Map Interactions
- **Zoom**: Mouse wheel or zoom controls
- **Pan**: Click and drag to move around
- **Markers**: Click for detailed popup information
- **Threat Indicators**: Color and animation based on severity

### Action Management
- **Interactive Checkboxes**: Click to mark actions as completed
- **Visual Feedback**: Completed actions show strikethrough text and muted colors
- **Progress Tracking**: Completion counter shows progress (e.g., "2/4 completed")
- **Persistent State**: Checkbox states remain until messages are cleared

### Responsive Design
- **Desktop**: Two-column layout maximizes screen real estate
- **Tablet**: Maintains two columns with adjusted spacing
- **Mobile**: Automatically stacks columns vertically for optimal viewing
- **Adaptive Maps**: Map height adjusts based on screen size

## Keyboard Shortcuts
- **Ctrl+F**: Focus on filter input
- **Escape**: Clear current filter
- **Space**: Toggle auto-scroll

## Troubleshooting

### Maps Not Loading
- Check internet connection (maps require external tile data)
- Verify coordinates are valid (latitude: -90 to 90, longitude: -180 to 180)
- Check browser console for JavaScript errors

### Messages Not Displaying
- Verify Solace connection is established
- Check topic subscription is active
- Ensure message payload is valid JSON matching the analysis schema

### Performance Issues
- Clear old messages periodically using "Clear Messages" button
- Disable auto-scroll for high-volume scenarios
- Consider filtering to reduce displayed messages
