* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100%;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.connected {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-indicator.disconnected {
    background-color: #f44336;
}

.status-indicator.connecting {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.controls, .message-controls {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #555;
}

input[type="text"] {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 200px;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.message-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 0 2rem 2rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.message-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-header h3 {
    color: #333;
    font-weight: 500;
}

.message-stats {
    color: #666;
    font-size: 0.9rem;
}

.message-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.message-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    transition: all 0.2s;
}

.message-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.message-item.new {
    animation: slideIn 0.3s ease-out;
    border-color: #28a745;
    background: #d4edda;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-timestamp {
    color: #666;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.message-topic {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.message-payload {
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
}

.footer {
    background: #f8f9fa;
    padding: 0.75rem 2rem;
    border-top: 1px solid #e0e0e0;
    font-size: 0.85rem;
    color: #666;
}

.config-info {
    display: flex;
    gap: 2rem;
}

.config-info span {
    display: flex;
    gap: 0.25rem;
}

.config-info span span {
    font-weight: 500;
    color: #333;
}

/* Scrollbar styling */
.message-list::-webkit-scrollbar {
    width: 8px;
}

.message-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.message-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
