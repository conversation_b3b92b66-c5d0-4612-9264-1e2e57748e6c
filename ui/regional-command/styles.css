* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100%;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.connected {
    background-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-indicator.disconnected {
    background-color: #f44336;
}

.status-indicator.connecting {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.controls, .message-controls {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #555;
}

input[type="text"] {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 200px;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.message-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    margin: 0 2rem 2rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.message-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-header h3 {
    color: #333;
    font-weight: 500;
}

.message-stats {
    color: #666;
    font-size: 0.9rem;
}

.message-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.message-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    transition: all 0.2s;
}

.message-item:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.message-item.new {
    animation: slideIn 0.3s ease-out;
    border-color: #28a745;
    background: #d4edda;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-timestamp {
    color: #666;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.message-topic {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.message-payload {
    color: #333;
    word-break: break-all;
    white-space: pre-wrap;
}

.footer {
    background: #f8f9fa;
    padding: 0.75rem 2rem;
    border-top: 1px solid #e0e0e0;
    font-size: 0.85rem;
    color: #666;
}

.config-info {
    display: flex;
    gap: 2rem;
}

.config-info span {
    display: flex;
    gap: 0.25rem;
}

.config-info span span {
    font-weight: 500;
    color: #333;
}

/* Scrollbar styling */
.message-list::-webkit-scrollbar {
    width: 8px;
}

.message-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.message-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Analysis Message Styles */
.analysis-message {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.analysis-message:hover {
    border-color: #667eea;
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e9ecef;
}

.threat-level-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.threat-level-number {
    font-size: 1.2rem;
    font-weight: 700;
}

.threat-critical {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    animation: pulse-critical 2s infinite;
}

.threat-high {
    background: linear-gradient(135deg, #fd7e14, #e55a00);
    color: white;
}

.threat-medium {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.threat-low {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.threat-minimal {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

@keyframes pulse-critical {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.analysis-content {
    display: grid;
    gap: 1.5rem;
}

.analysis-content h4 {
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
}

.analysis-content h4::before {
    content: '';
    width: 4px;
    height: 1rem;
    background: #667eea;
    margin-right: 0.5rem;
    border-radius: 2px;
}

.threat-levels-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.threat-levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
}

.threat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.threat-source {
    font-weight: 500;
    color: #495057;
}

.threat-value {
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    min-width: 2rem;
    text-align: center;
}

.threat-value.level-1, .threat-value.level-2 {
    background: #d4edda;
    color: #155724;
}

.threat-value.level-3, .threat-value.level-4 {
    background: #fff3cd;
    color: #856404;
}

.threat-value.level-5, .threat-value.level-6 {
    background: #f8d7da;
    color: #721c24;
}

.threat-value.level-7, .threat-value.level-8, .threat-value.level-9, .threat-value.level-10 {
    background: #f5c6cb;
    color: #721c24;
    animation: pulse-danger 1.5s infinite;
}

@keyframes pulse-danger {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.situation-section {
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.situation-summary {
    color: #1565c0;
    line-height: 1.6;
    font-weight: 500;
    margin: 0;
}

.actions-section {
    background: #fff3e0;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ff9800;
}

.actions-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.action-item {
    background: white;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid #ff9800;
    color: #e65100;
    font-weight: 500;
    position: relative;
    transition: all 0.2s;
}

.action-item:hover {
    background: #fff8e1;
    transform: translateX(4px);
}

.action-item::before {
    content: '▶';
    color: #ff9800;
    font-weight: bold;
    margin-right: 0.5rem;
}

.action-item:last-child {
    margin-bottom: 0;
}

.location-section {
    background: #f3e5f5;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #9c27b0;
}

.location-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.location-item {
    background: white;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e1bee7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.location-label {
    font-weight: 500;
    color: #7b1fa2;
}

.location-value {
    font-weight: 600;
    color: #4a148c;
    font-family: 'Courier New', monospace;
}

/* Enhanced message item for analysis messages */
.message-item.analysis-message.new {
    animation: slideInAnalysis 0.5s ease-out;
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

@keyframes slideInAnalysis {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .threat-levels-grid {
        grid-template-columns: 1fr;
    }

    .location-grid {
        grid-template-columns: 1fr;
    }

    .analysis-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .threat-level-badge {
        align-self: flex-end;
    }
}

/* Map Styles */
.map-container {
    background: white;
    border-radius: 8px;
    border: 2px solid #e1bee7;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.map-header {
    background: linear-gradient(135deg, #9c27b0, #7b1fa2);
    color: white;
    padding: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.map-title {
    font-size: 0.9rem;
}

.map-coords {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.location-map {
    height: 200px;
    width: 100%;
    position: relative;
}

.map-placeholder, .map-error {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #6c757d;
    font-weight: 500;
}

.map-placeholder-text {
    font-size: 1rem;
}

/* Custom Threat Markers */
.threat-marker {
    background: transparent !important;
    border: none !important;
}

.threat-marker-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
    border: 3px solid rgba(255,255,255,0.8);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    position: relative;
    transition: all 0.3s ease;
}

.threat-marker-inner:hover {
    transform: scale(1.1);
}

.threat-marker-level {
    z-index: 2;
    position: relative;
}

.threat-marker-pulse {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.6;
    animation: marker-pulse 2s infinite;
}

@keyframes marker-pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Map Popup Styles */
.leaflet-popup-content-wrapper {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
}

.map-popup {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-width: 250px;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.popup-threat-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.popup-content p {
    margin: 0.5rem 0;
    font-size: 0.85rem;
    line-height: 1.4;
}

.popup-content strong {
    color: #495057;
}

/* Responsive map adjustments */
@media (max-width: 768px) {
    .location-map {
        height: 150px;
    }

    .map-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .location-content {
        gap: 0.75rem;
    }
}
