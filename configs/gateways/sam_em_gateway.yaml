# Example Configuration for SAM Event Mesh Gateway Plugin
#
# This file provides a template for configuring the Event Mesh Gateway.
# It should be included or adapted into your main Solace Agent Mesh Host YAML file.

log:
  stdout_log_level: INFO
  log_file_level: DEBUG # Changed from INFO to DEBUG to capture ADK INFO logs
  log_file: sam_em_gateway.log

# !include shared_config.yaml
!include ../shared_config.yaml

# --- App Definition (to be placed under the 'apps:' list in your main config) ---
apps:
  - name: my_event_mesh_gateway_app # Unique name for this gateway app instance
    app_module: sam_event_mesh_gateway.app # Points to the plugin's App class
    app_base_path: plugins/sam-event-mesh-gateway/src # Base path for the app module
    broker: # Standard SAC broker config for the A2A control plane
      <<: *broker_connection

    app_config:
      namespace: ${NAMESPACE}
      gateway_id: "event-mesh-gw-01" # Unique ID for this gateway instance
      artifact_service: # Configuration for shared ADK Artifact Service
        type: "filesystem"
        base_path: "/tmp/samv2"
      authorization_service:
        type: "default_rbac"
        role_definitions_path: "configs/auth/dev-roles.yaml"
        user_assignments_path: "configs/auth/dev-users.yaml"
      # Force all user identities to sam_dev_user (overrides web-client-xxxxx)
      force_user_identity: "sam_dev_user"
      # Fallback for null identities (kept for completeness)
      default_user_identity: "sam_dev_user"

      # --- Event Mesh Gateway Specific Parameters ---
      event_mesh_broker_config: # For the data plane Solace client
        broker_url: ${DATAPLANE_SOLACE_BROKER_URL} # Can be same or different from control plane
        broker_vpn: ${DATAPLANE_SOLACE_BROKER_VPN}
        broker_username: ${DATAPLANE_SOLACE_BROKER_USERNAME}
        broker_password: ${DATAPLANE_SOLACE_BROKER_PASSWORD}
        # Other data plane client settings (e.g., client_name, reconnection_strategy)

      event_handlers: # List of handlers for incoming Solace messages
        - name: "generic_json_event_handler"
          subscriptions:
            - topic: "ed_demo/abc/jira/issue/create/>"
              qos: 1
          input_expression: "template:Summarize this new Jira issue: {{json://input.payload}}"
          payload_encoding: "utf-8"
          payload_format: "json"
          user_identity_expression: "static:sam_dev_user"
          on_success: "success_response_handler"
          on_error: "error_response_handler"
          target_agent_name: "OrchestratorAgent" 

        - name: "image_analysis_handler"
          subscriptions:
            - topic: "military/surveillance/image/submitted/>" #military/surveillance/image/submitted/v1/{regionid}/{baseId}/{lattiude}/{longitude}/{imageId}
          payload_format: "text"
          user_identity_expression: "static:sam_dev_user"
          artifact_processing:
            extract_artifacts_expression: "input.payload"
            artifact_definition:
              # Corrected expression for filename
              filename: "template:image-{{text://input.topic_levels:9}}.jpg"
              content: "list_item:"
              mime_type: "static:image/jpg"
              content_encoding: "static:base64"
          input_expression: |
            template:Describe the security situation at forward operating base {{text://input.topic_levels:6}}.   
            Your description must include recent field reports from the forward operating base.   
            Your description must include analysis of the threat level of this image, which was recently taken outside the base. 
            Respond with only a JSON object in the payload.  Do not include any preceeding or succeeding text.  
            JSON payload must contain: 
            -	your analysis of the threat level (on a scale of 1 to 10) from field reports
            -	your analysis of the threat level (on a scale of 1 to 10) from image analysis
            -	your analysis of the threat level (on a scale of 1 to 10) combining field report and image analysis
            -	100 word summary of the security situation
            -	Three recommended next step actions            
            - region_id  = {{text://input.topic_levels:5}}
            - base_id =  {{text://input.topic_levels:6}}
            -	latitude = {{text://input.topic_levels:7}} 
            -	longitude = ({{text://input.topic_levels:8}}

            JSON should be formatted according to this schema:
            {
            "$schema": "http://json-schema.org/draft-04/schema#",
            "type": "object",
            "properties": {
              "threat_level_field_reports": {
                "type": "integer"
              },
              "threat_level_image_analysis": {
                "type": "integer"
              },
              "threat_level_combined": {
                "type": "integer"
              },
              "security_situation_summary": {
                "type": "string"
              },
              "recommended_actions": {
                "type": "array",
                "items": [
                  {
                    "type": "string"
                  },
                  {
                    "type": "string"
                  },
                  {
                    "type": "string"
                  }
                ]
              },
              "regionId": {
                "type": "integer"
              },
              "baseId": {
                "type": "integer"
              },
              "latitude": {
                "type": "number"
              },
              "longitude": {
                "type": "number"
              }
            },
            "required": [
              "threat_level_field_reports",
              "threat_level_image_analysis",
              "threat_level_combined",
              "security_situation_summary",
              "recommended_actions",
              "region_id",
              "base_id",
              "latitude",
              "longitude"
            ]
            }
          target_agent_name: "OrchestratorAgent"
          on_success: "image_description_response_handler"
          on_error: "error_response_handler"
          forward_context:
            # Corrected expression for forwarding context
            region_id: "input.topic_levels:5"
            base_id:  "input.topic_levels:6"
            latitude: "input.topic_levels:7"
            longitude: "input.topic_levels:8"

        # --- Example: Handler with Artifact Processing ---
        # This handler processes a JSON payload containing a list of base64-encoded documents.
        # It creates an artifact for each document and then calls an agent.
        # - name: "json_with_embedded_artifact_handler"
        #   subscriptions:
        #     - topic: "acme/documents/new"
        #   payload_format: "json"
        #   # --- New Artifact Processing Block ---
        #   artifact_processing:
        #     extract_artifacts_expression: "input.payload:documents" # Points to a list in the payload
        #     artifact_definition:
        #       # These expressions are evaluated for EACH item in the 'documents' list
        #       filename: "list_item:docName"
        #       content: "list_item:docContent"
        #       mime_type: "list_item:docType"
        #       content_encoding: "static:base64" # Explicitly state the content is a base64 string
        #   # --- Main Prompt ---
        #   input_expression: "template:Please process insurance case {{text://input.payload:caseId}}. The relevant documents have been attached."
        #   target_agent_name: "ClaimsProcessingAgent"
        #   on_success: "success_response_handler"
        #   on_error: "error_response_handler"
        #   forward_context:
        #     correlation_id: "input.payload:caseId"

  # Example of a second event handler, commented out
  #       - name: "text_event_to_specific_agent"
  #         subscriptions:
  #           - topic: "external/systemB/events/text/>"
  #         input_expression: "template:User query from System B: {{text://input.payload}}"
  #         payload_encoding: "utf-8" # Or "none" if payload is already string
  #         payload_format: "text"
  #         on_success: "text_response_to_systemB"
  #         target_agent_name_expression: "static:TextAnalysisAgent" # Example of static via expression

      output_handlers: # Optional: List of handlers for publishing A2A responses
        - name: "success_response_handler"
          max_file_size_for_base64_bytes: 5242880 # 5MB limit for embedded files
          topic_expression: "template:event_mesh/responses/{{text://user_data.forward_context:correlation_id}}"
          payload_expression: "task_response:text" # Use the simplified payload's text field
          payload_encoding: "utf-8"
          payload_format: "text"
          # output_schema: # Optional: Embedded JSON schema for validation
          #   type: "object"
          #   properties:
          #     processed_data: { "type": "string" }
          #   required: ["processed_data"]
          # on_validation_error: "log" # Or "drop"
        - name: "error_response_handler"
          topic_expression: "template:event_mesh/errors/{{text://user_data.forward_context:correlation_id}}"
          payload_expression: "task_response:a2a_task_response.error" # Send the full error object
          payload_encoding: "utf-8"
          payload_format: "json"

        - name: "image_description_response_handler"
          # military/intelligence/report/generated/v1/{regionid}/{baseId}
          topic_expression: "template:military/intelligence/report/generated/v1/{{text://user_data.forward_context:region_id}}/{{text://user_data.forward_context:base_id}}"
          payload_expression: "task_response:text"
          payload_encoding: "utf-8"
          payload_format: "text"

  # Example of a second output handler, commented out
  #       - name: "text_response_to_systemB"
  #         topic_expression: "template:external/systemB/responses/{{text://task_response:id}}"
  #         payload_expression: "task_response:status.message.parts.0.text" # Direct access
  #         payload_encoding: "utf-8"
  #         payload_format: "text"
